# Stage 0: Base image with Python and uv
FROM python:3.11-slim AS base

# Install only necessary system dependencies
# This layer is cached unless these packages change
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libcairo2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    shared-mime-info \
    libxml2-dev \
    libxslt-dev \
    libgbm1 \
    fonts-liberation \
    fonts-roboto \
    curl \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy uv binaries from upstream image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set working directory
WORKDIR /app

# Stage 1: Python dependencies only
FROM base AS dependencies

# Copy only lockfiles first to cache dependency install
COPY pyproject.toml poetry.lock* uv.lock ./
ENV UV_HTTP_TIMEOUT=120 
RUN uv sync --frozen --no-cache

# Stage 2: Install Playwright + Chromium
FROM dependencies AS playwright
RUN uv run playwright install chromium --with-deps

# Stage 3: Final runtime layer
FROM playwright AS app

# Copy application code (done last to maximize caching)
COPY . .

EXPOSE 8000

# CMD ["uv", "run", "uvicorn", "main:app" "--host", "0.0.0.0", "--port", "8000"]

CMD sh -c "uv run uvicorn main:app --host 0.0.0.0 --port 8000"
