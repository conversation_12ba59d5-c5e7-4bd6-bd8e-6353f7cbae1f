pipeline {
    agent any

    environment {
        APP_REPLICAS = '2'
        EXPOSED_PORT = '8200'
    }

    stages {
        stage('Setup') {
            steps {
                withCredentials([file(credentialsId: 'echo_backend_env', variable: 'ENV_FILE')]) {
                    sh '''
                        cp "$ENV_FILE" .env
                        echo "APP_INSTANCES=${APP_REPLICAS}" >> .env
                        echo "EXPOSED_PORT=${EXPOSED_PORT}" >> .env
                    '''
                }
            }
        }

        stage('Deploy with Auto-scaling') {
            steps {
                script {
                    try {
                        sh '''
                            docker compose down || true
                            docker compose build --no-cache
                            docker compose up -d

                            echo "Waiting for services..."
                            sleep 30

                            echo "Checking deployment..."
                            docker compose ps
                        '''
                        echo "✅ Deployed with built-in auto-scaling"
                    } catch (Exception err) {
                        currentBuild.result = 'FAILURE'
                        throw err
                    }
                }
            }
        }
    }

    post {
        always {
            sh 'docker system prune -f'
        }
    }
}
