import sys
import os
import re
import asyncio
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from fastapi import UploadFile
async def get_filename(title):
    filename = title.lower().replace(" ", "_").replace("/", "_").replace("|", "_")
    filename = re.sub(r'[^a-zA-Z0-9_\-]+', '', filename)
    
    # Add leading zeros to make 4-digit filename
    if filename.isdigit():
        filename = f"{int(filename):04d}"
    else:
        filename = f"{filename.zfill(4)}"
    
    return filename + "_website.pdf"

async def save_pdf(url: str, filename: str = None):
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        context = await browser.new_context(
            viewport={"width": 1920, "height": 1080},
            device_scale_factor=1
        )
        page = await context.new_page()
        
        await page.goto(url)
        
        await page.wait_for_load_state('domcontentloaded', timeout=30000)
        await page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
        await asyncio.sleep(2)
        
        # Disable all animations globally
        await page.evaluate('''() => {
            document.querySelectorAll('*').forEach(el => {
                el.style.transitionDuration = '0s';
                el.style.animationDuration = '0s';
                el.style.transform = 'none !important';
                el.style.transitionProperty = 'none !important';
                el.style.animation = 'none !important';
            });
        }''')
        
        # Remove modals/popups
        await page.evaluate('''() => {
            const modalSelectors = [
                '.modal', '.overlay', '.popup', '[role="dialog"]',
                '[aria-modal="true"]', '[data-modal]', '[data-popup]'
            ];
            modalSelectors.forEach(selector => {
                const modal = document.querySelector(selector);
                if (modal) modal.remove();
            });
        }''')
        
        await handle_sliders(page)
        
        # Wait for images (with retry)
        # await page.waitForFunction('''() => {
        #     return Array.from(document.querySelectorAll('img')).every(
        #         img => img.complete && img.naturalHeight !== 0
        #     );
        # }''', timeout=30000)
        
        # Final adjustments
        await page.evaluate('window.scrollTo(0, 0)')
        await asyncio.sleep(1)
        
        content = await page.content()
        soup = BeautifulSoup(content, "html.parser")
        title = soup.title.string if soup.title else "untitled"
        output_filename = await get_filename(filename or title)
        output_path = f"{output_filename}.pdf"

        pdf=await page.pdf(
            path=output_path,
            format="A4",
            landscape=True,
            scale=0.85,
            margin={"top": "0px", "bottom": "0px", "left": "0px", "right": "0px"},
            print_background=True,
            prefer_css_page_size=False
        )
        
        await browser.close()
        print(f"PDF saved as {output_path}")
        
        return UploadFile(filename=output_filename, file=open(output_path, "rb"), headers={"Content-Type": "application/pdf"})
async def handle_sliders(page):
    slider_types = [
        {
            'name': 'Slick Slider',
            'selectors': {
                'container': '.slick-slider',
                'slide': '.slick-slide',
                'list': '.slick-list'
            },
            'styles': {
                '.slick-slider': {'overflow': 'visible !important'},
                '.slick-slide': {'display': 'inline-block !important', 'position': 'static !important', 'opacity': '1 !important'},
                '.slick-list': {'overflow': 'visible !important', 'position': 'relative !important'}
            }
        },
        {
            'name': 'Owl Carousel',
            'selectors': {
                'container': '.owl-carousel',
                'slide': '.owl-item',
                'list': '.owl-stage'
            },
            'styles': {
                '.owl-carousel': {'overflow': 'visible !important'},
                '.owl-item': {'display': 'inline-block !important', 'position': 'relative !important', 'opacity': '1 !important'},
                '.owl-stage': {'position': 'relative !important', 'overflow': 'visible !important'}
            }
        },
        {
            'name': 'Swiper Slider',
            'selectors': {
                'container': '.swiper-container',
                'slide': '.swiper-slide',
                'list': '.swiper-wrapper'
            },
            'styles': {
                '.swiper-container': {'overflow': 'visible !important'},
                '.swiper-slide': {'display': 'inline-block !important', 'position': 'relative !important', 'opacity': '1 !important'},
                '.swiper-wrapper': {'transition': 'none !important', 'transform': 'translate3d(0, 0, 0) !important'}
            }
        },
        {
            'name': 'Bootstrap Carousel',
            'selectors': {
                'container': '.carousel',
                'slide': '.carousel-item',
                'list': '.carousel-inner'
            },
            'styles': {
                '.carousel-item': {'display': 'block !important', 'position': 'static !important', 'opacity': '1 !important'},
                '.carousel-inner': {'overflow': 'visible !important'}
            }
        }
    ]

    for slider in slider_types:
        # Check if slider exists
        exists = await page.evaluate(f'() => !!document.querySelector("{slider["selectors"]["container"]}")')
        if not exists:
            continue

        # Apply styles
        style_rules = []
        for selector, styles in slider['styles'].items():
            style = f'{selector} {{ {" ".join([f"{k}:{v};" for k,v in styles.items()])} }}'
            style_rules.append(style)
        
        # Inject styles
        await page.evaluate(f'''(css) => {{
            const style = document.createElement('style');
            style.textContent = css;
            document.head.append(style);
        }}''', '\n'.join(style_rules))
        
        # Force layout update
        await page.evaluate('() => requestAnimationFrame(() => requestAnimationFrame(() => {}))')
        await asyncio.sleep(0.5)

        # Remove slide visibility toggling
        await page.evaluate(f'''() => {{
            const slides = document.querySelectorAll("{slider['selectors']['slide']}");
            slides.forEach(slide => {{
                slide.style.display = 'inline-block !important';
                slide.style.position = 'relative !important';
                slide.style.opacity = '1 !important';
            }});
        }}''')

        # Remove event listeners
        await page.evaluate(f'''() => {{
            const container = document.querySelector("{slider['selectors']['container']}");
            if (container) {{
                const events = ['click', 'mouseover', 'touchstart'];
                events.forEach(event => {{
                    container.removeEventListener(event, () => {{}});
                }});
            }}
        }}''')


async def handle_urls(urls:list)->list[UploadFile]:
    tasks=[]
    for url in urls:
        tasks.append(save_pdf(url))
    pdfs=await asyncio.gather(*tasks)
    return pdfs

if __name__ == "__main__":
    import asyncio
    asyncio.run(save_pdf("https://www.divinecosmeticsurgery.com/liposuction-surgery-cost.php"))