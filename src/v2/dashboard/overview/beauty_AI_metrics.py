
import asyncio
from typing import Any, Dict, List
from src.models.chat_hist import ModelRunRequest
from src.helper.resolve_llm import resolve_llm
from datetime import datetime, timedelta, time
import re

from collections import defaultdict

def avg_processing_time(db, start_date, end_date) -> dict:
    pipeline = [
        {
            "$match": {
                "response.request_time": {
                    "$gte": start_date,
                    "$lt": end_date
                }
            }
        },
        {
            "$group": {
                "_id": None,
                "average_time": {"$avg": "$response.processing_time"},
                "total_files": {"$sum": 1}
            }
        }
    ]

    result = list(db.ai_response.aggregate(pipeline))

    avg_time = result[0]['average_time'] if result else 0.0
    total_files = result[0]['total_files'] if result else 0

    # return {
    #     "name": f"AI processing Avg Time from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
    #     "avgtime": avg_time,
    #     "total_files": total_files
    # }
    return {

        "avgtime": avg_time
    }


def channel_message_count(db, start_date, end_date) -> list[dict]:
    pipeline = [
        {
            "$match": {
                "response.request_time": {
                    "$gte": start_date,
                    "$lt": end_date
                }
            }
        },
        {
            "$group": {
                "_id": "$request.channel",
                "count": {"$sum": 1}
            }
        },
        {
            "$sort": {
                "count": -1
            }
        }
    ]

    result = list(db.ai_response.aggregate(pipeline))
    return result


def CTA_count(db, start_date, end_date) -> dict:
    cta_pipeline = [
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "channel": {"$ne":"Playground"}
            }
        },
        {
            "$group": {
                "_id": {
                    "type": "$type",
                    "status": "$status"
                },
                "count": {"$sum": 1}
            }
        }
    ]

    docs_with_cta_pipeline = [
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "request.channel": {"$ne":"Playground"},
                "response.call_to_action": {
                    "$exists": True,
                    "$not": {
                        "$in": [[], [None], None]
                    }
                }
            }
        },
        {
            "$count": "docs_with_cta"
        }
    ]

    total_pipeline = [
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "request.channel":{"$ne":"Playground"},
                "response.chat_data": {
                "$elemMatch": {
                    "role": "user"
                }
            }
            }
        },
        {
            "$count": "total_documents"
        }
    ]
    cta_result = list(db.cta.aggregate(cta_pipeline))
    docs_with_cta_result = list(db.ai_response.aggregate(docs_with_cta_pipeline))
    total_result = list(db.ai_response.aggregate(total_pipeline))
    # Initialize variables with default values
    cta_raised_percentage = 0
    total_documents = 0
    if total_result:
        total_documents = total_result[0]["total_documents"]
    # Calculate percentage only if we have both values
    if docs_with_cta_result:
        cta_raised_percentage = (docs_with_cta_result[0]["docs_with_cta"] / total_documents) * 100

    # Format result
    formatted_data = [
        {
            "type": entry["_id"]["type"],
            "status": entry["_id"]["status"],
            "count": entry["count"]
        }
        for entry in cta_result
    ]

    final_output = {
        "data": formatted_data,
        "total_chats": total_documents,
        "CTA_raised_percentage": cta_raised_percentage
    }

    return final_output



def CTA_type_count(db, start_date, end_date) -> list[dict]:
    pipeline = [
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "channel": {"$ne":"Playground"}
            }
        },
        {
            "$group": {
                "_id": {
                    "type": "$type",
                    "status": "$status"
                },
                "count": {"$sum": 1}
            }
        },
        {
            "$group": {
                "_id": "$_id.type",
                "statuses": {
                    "$push": {
                        "status": "$_id.status",
                        "count": "$count"
                    }
                },
                "total": {"$sum": "$count"}
            }
        },
        {
            "$project": {
                "_id": 0,
                "type": "$_id",
                "statuses": 1,
                "total": 1
            }
        }
    ]

    result = list(db.cta.aggregate(pipeline))
    return result


def evaluation_count(db, start_date, end_date) -> list[dict]:
    pipeline = [
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },

                "evaluation": {
                    "$exists": True,
                    "$ne": None,
                    "$ne": ""
                }
            }
        },
        {
            "$group": {
                "_id": "$evaluation",
                "count": {"$sum": 1}
            }
        }
    ]

    result = list(db.evaluations.aggregate(pipeline))
    return result




def message_and_cta_count(db, start_date, end_date) -> list[dict]:
    pipeline = [
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "request.channel": {"$ne":"Playground"},
                "response.chat_data": {
                    "$elemMatch": {
                        "role": "user"
                    }
                }
            }
        },
        {
            "$group": {
                "_id": {
                    "$dateToString": {
                        "format": "%Y-%m-%d",
                        "date": "$created_at"
                    }
                },
                
                "message_count": {"$sum": 1},
                "cta_count": {
                    "$sum": {
                        "$size": {
                            "$ifNull": ["$response.call_to_action", []]
                        }
                    }
                }
            }
        },
        {
            "$sort": {
                "_id": 1
            }
        }
    ]

    result = list(db.ai_response.aggregate(pipeline))

    # Convert result to dictionary for easy lookup
    data_dict = {item["_id"]: {"message_count": item["message_count"], "cta_count": item["cta_count"]} for item in result}

    # Generate all dates between start and end
    complete_data = []
    current_date = start_date
    while current_date < end_date:
        date_str = current_date.strftime("%Y-%m-%d")
        data = data_dict.get(date_str, {"message_count": 0, "cta_count": 0})
        complete_data.append({
            "_id": date_str,
            "message_count": data["message_count"],
            "cta_count": data["cta_count"]
        })
        current_date += timedelta(days=1)

    return complete_data
def unique_users_per_day(db, start_date, end_date) -> list[dict]:
    pipeline = [
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "customer_id": { "$exists": True, "$ne": None }
            }
        },
        {
            "$group": {
                "_id": {
                    "$dateToString": {
                        "format": "%Y-%m-%d",
                        "date": "$created_at"
                    }
                },
                "unique_users": {
                    "$addToSet": "$customer_id"
                }
            }
        },
        {
            "$project": {
                "_id": 0,
                "date": "$_id",
                "user_count": { "$size": "$unique_users" }
            }
        },
        {
            "$sort": { "date": 1 }
        }
    ]

    result = list(db.customers.aggregate(pipeline))

    # Convert result to dictionary for easy lookup
    data_dict = {item["date"]: item["user_count"] for item in result}

    # Generate all dates between start and end
    complete_data = []
    current_date = start_date
    while current_date < end_date:
        date_str = current_date.strftime("%Y-%m-%d")
        complete_data.append({
            "date": date_str,
            "user_count": data_dict.get(date_str, 0)
        })
        current_date += timedelta(days=1)

    return complete_data





def language_count(db, start_date, end_date) -> list[dict]:
    pipeline = [
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "request.channel": {"$ne":"Playground"},
                "response.language": {"$nin": [None, "", " "]}
            }
        },
        {
            "$group": {
                "_id": "$response.language",
                "count": {"$sum": 1}
            }
        }
    ]

    raw_result = list(db.ai_response.aggregate(pipeline))
    
    # Post-process to clean and merge
    clean_counts = defaultdict(int)
    for item in raw_result:
        lang = item["_id"].strip().capitalize()
        clean_counts[lang] += item["count"]
    
    result = [{"_id": lang, "count": count} for lang, count in clean_counts.items()]
    return result





async def message_topic_count(db, start_date, end_date) -> dict:
    """
    Aggregates topic data across multiple chats for users using an optimized MongoDB pipeline,
    grouping by each day (YYYY-MM-DD), including handling subtopics with no name.
    """
    start_date = datetime.combine(start_date.date(), time.min)
    end_date = datetime.combine(end_date.date(), time.max)
    
    pipeline = [
        {
            '$match': {
                'created_at': {
                    '$gte': start_date,
                    '$lt': end_date
                },
                "request.channel":{"$ne":"Playground"},
                'response.topic': {
                    '$exists': True,
                    '$ne': {}
                }
            }
        },
        {
            '$project': {
                'topic': '$response.topic',
                'date': { '$dateToString': { 'format': "%Y-%m-%d", 'date': "$created_at" } }
            }
        },
        {
            '$group': {
                '_id': None,
                'all_topics': { '$push': { 'topic': '$topic', 'date': '$date' } }
            }
        },
        {
            '$unwind': '$all_topics'
        },
        {
            '$group': {
                '_id': '$all_topics.topic.topic_id',
                'topic_name': { '$first': '$all_topics.topic.topics' },
                'date': { '$first': '$all_topics.date' }
            }
        },
        {
            '$group': {
                '_id': None,
                'topics_list': { '$push': { 'topics': '$topic_name', 'date': '$date' } }
            }
        },
        {
            '$unwind': '$topics_list'
        },
        {
            '$unwind': '$topics_list.topics'
        },
        {
            '$unwind': {
                'path': '$topics_list.topics.sub_topics',
                'preserveNullAndEmptyArrays': True
            }
        },
        {
            '$addFields': {
                'topics_list.topics.sub_topics.name': {
                    '$ifNull': ['$topics_list.topics.sub_topics.name', None]  # Add 'null' if no subtopic name
                }
            }
        },
        {
            '$group': {
                '_id': {
                    'date': '$topics_list.date',
                    'topic': '$topics_list.topics.topic',
                    'subtopic': '$topics_list.topics.sub_topics.name'
                },
                'count': { '$sum': 1 }
            }
        },
        {
            '$group': {
                '_id': { 'date': '$_id.date', 'topic': '$_id.topic' },
                'count': { '$sum': '$count' },
                'sub_topics': {
                    '$push': {
                        'name': '$_id.subtopic',
                        'count': '$count'
                    }
                }
            }
        },
        {
            '$group': {
                '_id': '$_id.date',
                'topics': {
                    '$push': {
                        'topic': '$_id.topic',
                        'count': '$count',
                        'sub_topics': {
                            '$filter': {
                                'input': '$sub_topics',
                                'as': 'sub',
                                'cond': { '$ne': ['$$sub.name', None] }
                            }
                        }
                    }
                }
            }
        },
        {
            '$project': {
                '_id': 0,
                'date': '$_id',
                'topics': 1
            }
        },
        {
            '$sort': { 'date': 1 }  # Sort by date ascending
        }
    ]
    # print(pipeline)
    result = await(await db.ai_response.aggregate(pipeline)).to_list(length=None)

    # Reorganize the result into the desired format: date as keys
    formatted_result = {}
    for doc in result:
        date = doc['date']
        formatted_result[date] = {
            "_id": date,
            "topics": doc['topics']
        }

    return formatted_result



async def topic_identifier_v2(db, user_id: str) -> list[dict]:
    count_pipeline = [
        {
            "$match": {
                "request.user_id": user_id
            }
        },
        {
            "$group": {
                "_id": "$request.user_id",
                "total_messages": {"$sum": 1}
            }
        },
        {
            "$project": {
                "user_id": {"$toString": "$_id"},  # Convert ObjectId to string
                "_id": 0,
                "total_messages": 1
            }
        }
    ]

    total_messages_result = list(db.ai_response.aggregate(count_pipeline))

    if total_messages_result:
        total_messages = total_messages_result[0].get('total_messages', 0)

        if total_messages % 10 == 0:
            messages_pipeline = [
                {
                    "$match": {
                        "request.user_id": user_id
                    }
                },
                {
                    "$sort": {"response.request_time": -1}  # Sort messages by time (descending)
                },
                {
                    "$limit": 10
                },
                {
                    "$project": {
                        "_id": 0,
                        "request.message": 1,
                        "response.reply": 1,
                    }
                }
            ]
            last_10_messages = list(db.ai_response.aggregate(messages_pipeline))
            topic_info = await generate_message_topic_v2(last_10_messages, db)


            # return {"total_messages": total_messages, "last_10_messages": last_10_messages}
            return topic_info


        return {"total_messages": total_messages}



    return {"total_messages": 0}


async def human_reply(db, start_date, end_date):
    pipeline = [
    {"$unwind": "$response.chat_data"},
    {"$match": {
        "response.chat_data.created_at": {
            "$gte": start_date,
            "$lte": end_date
        }
    }},
    {"$group": {
        "_id": "$_id",
        "roles": {"$addToSet": "$response.chat_data.role"}
    }},
    {"$project": {
        "has_assistant": {"$in": ["assistant", "$roles"]},
        "has_agent": {"$in": ["agent", "$roles"]},
        "only_agent": {
            "$and": [
                {"$in": ["agent", "$roles"]},
                {"$not": {"$in": ["assistant", "$roles"]}}
            ]
        }
    }},
    {"$group": {
        "_id": None,
        "assistant_messages": {"$sum": {"$cond": ["$has_assistant", 1, 0]}},
        "human_messages": {"$sum": {"$cond": ["$has_agent", 1, 0]}},
        "human_messages_only": {"$sum": {"$cond": ["$only_agent", 1, 0]}},
        "total_messages": {"$sum": 1}
    }}
]


    result =  db.ai_response.aggregate(pipeline).to_list(length=None)
    return result[0] if result else {"total_messages": 0,  "human_messages": 0}








from src.helper.resolve_llm import resolve_llm
import re

def extract_tags(topic_analysis: str) -> list[str]:
    raw_tags = re.findall(r"##(\w+(?:\s\w+)*)", topic_analysis)

    # Normalize and clean newlines/numbers like "risk\n2" → "risk"
    cleaned_tags = [re.split(r'\n|\d+$', tag.strip())[0].strip().lower() for tag in raw_tags]

    # Return unique tags
    return list(set(cleaned_tags))

async def generate_message_topic_v2(usermessage: str, db) -> Dict[str, Any]:
    PROMPT_NAME = "find_chat_topic"
    prompt = db["prompt"].find_one({"name": PROMPT_NAME})
    try:
        llm = resolve_llm(prompt.get("model"))
        # First await the completion coroutine
        completion = await llm.acomplete(
            prompt.get("text").format(messages_str=usermessage),
            formatted=True
        )
        # Then access the text attribute
        topic_analysis = completion.text if hasattr(completion, 'text') else str(completion)

        return {
            "topic_analysis": topic_analysis,
            "tags": extract_tags(topic_analysis),
            "time": datetime.now()
        }
    except Exception as e:
        raise e


