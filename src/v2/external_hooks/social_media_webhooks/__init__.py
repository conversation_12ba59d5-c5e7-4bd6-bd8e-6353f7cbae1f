"""
Consolidated social media webhooks module.

This module provides a unified interface for social media platform integrations
including Facebook, Instagram, and WhatsApp through the Sociar API.

The module consolidates previously scattered webhook APIs into a single,
clean, modern API structure with consistent endpoints and patterns.
"""

from fastapi import APIRouter
from .routes import router as routes_router
from .webhook import router as webhook_router

# Create the main social media router
social_media_router = APIRouter()

# Include all sub-routers (modern unified APIs only)
social_media_router.include_router(routes_router)
social_media_router.include_router(webhook_router)

# Export main components for external use
from src.v2.external_hooks.social_media_webhooks.client import SociarClient, get_sociar_client
from src.v2.external_hooks.social_media_webhooks.models import (
    SocialMediaMessageRequest,
    WhatsAppTemplate,
    WhatsAppMessageRequest,
    SocialMediaResponse
)

__all__ = [
    'social_media_router',
    'SociarClient',
    'get_sociar_client',
    'SocialMediaMessageRequest',
    'FacebookMessageRequest',
    'InstagramMessageRequest',
    'WhatsAppTemplate',
    'WhatsAppMessageRequest',
    'SocialMediaResponse',
    'send_facebook_message',
    'send_instagram_message',
    'send_whatsapp_message',
    'send_welcome_message'
]
