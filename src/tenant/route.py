
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import json
from dotenv import load_dotenv
import os
import asyncio
from pymongo import AsyncMongoClient

from src.core.database import get_admin_db
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.tenant.module.create_tenent import Tenant


tenant_router = APIRouter(
    prefix="/tenants",
    tags=["Tenants"],
)

class TenantCreate(BaseModel):
    tenant_name: str
    tenant_slug: str





@tenant_router.post("/create-tenents")
async def create_tenant(
    request: TenantCreate=Depends(),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    print("current_user",current_user.user.role,current_user.user.username)
    if current_user.user.role != "admin" or current_user.user.username != "superadmin":
        raise HTTPException(status_code=403, detail="You are not authorized for this action")
    if not request.tenant_name or not request.tenant_slug:
        raise HTTPException(status_code=400, detail="Tenant name and slug are required")
    
    requirements=get_admin_db().requirements.find_one({"name":"new_tenant_requirement"})
    
    tenant = Tenant(request.tenant_name, request.tenant_slug,requirements)
    try:
        await tenant._register_tenant()
        await tenant._preapre_client_database()
        await tenant._insert_default_data()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    return {"message": "Tenant created successfully make sure to change tenant_id in data"}

