import os
import mimetypes
import asyncio
import fitz
import pdfkit
from typing import Optional, List, Dict
from datetime import timed<PERSON><PERSON>
from docx import Document as Docx_bytes
from urllib.parse import quote
from fastapi import HTTPException, UploadFile
from src.reply.minio_client import <PERSON><PERSON><PERSON><PERSON>, MinIOConfig
from io import BytesIO
from llama_index.core.schema import Document 
from src.helper import logger
from itertools import repeat
from datetime import datetime
import hashlib
import re
from src.models.user import UserTenantDB

loggers=logger.setup_new_logging(__name__)


async def convert_txt_to_pdf(text: str) -> bytes:
    """Convert a text string to a PDF (as bytes)."""
    html_content = f"<html><body><pre>{text}</pre></body></html>"
    return await asyncio.to_thread(pdfkit.from_string, html_content, False)


async def convert_docx_to_pdf(docx_content: bytes) -> bytes:
    """Convert a DOCX file (as bytes) to a PDF (as bytes)."""
    doc = Docx_bytes(BytesIO(docx_content))
    text = "\n".join([p.text for p in doc.paragraphs])
    return await convert_txt_to_pdf(text)

async def process_files(files: Optional[List[UploadFile]]) -> List[UploadFile]:
    if not files:
        return []
    loggers.info(f"Processing files...,{[file.filename for file in files]}")
    
    
    
    pdf_bytes = []

    for file in files:
        file_ext = os.path.splitext(file.filename)[1].lower()
        file_content = await file.read()
        
        
        if file_ext == ".pdf":
            pdf_bytes.append(UploadFile(filename=file.filename, file=BytesIO(file_content), headers={"Content-Type": "application/pdf"}))
        elif file_ext == ".txt":
            pdf_byte = await convert_txt_to_pdf(file_content.decode('utf-8'))
            pdf_bytes.append(UploadFile(filename=file.filename, file=BytesIO(pdf_byte), headers={"Content-Type": "application/pdf"}))
        elif file_ext == ".docx":
            pdf_byte = await convert_docx_to_pdf(file_content)
            pdf_bytes.append(UploadFile(filename=file.filename, file=BytesIO(pdf_byte), headers={"Content-Type": "application/pdf"}))
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_ext}")
    
    return pdf_bytes


    return pdfs

from collections import deque

async def extract_text_and_images(Pdfs: List["UploadFile"], minio_client: MinIOClient) -> List[Dict]: # List["UploadFile"] wtf is this? just List[Uploadfile] works
    results = []
    
    for pdf in Pdfs:
        doc_bytes = await pdf.read()
        doc = fitz.open(stream=doc_bytes, filetype="pdf")
        pdf_name = pdf.filename
        minio_file_name = minio_client.upload_bytes(pdf_name, doc_bytes, "Files")
        
        pdf_data = []
        prev_text_blocks = deque()  # Store previous short text blocks
        prev_images = deque()  # Store images from pages with little/no text
        images=set()

        for page in doc:
            page_data = {
                "page_number": page.number + 1,
                "text_blocks": [],
                "images": []
            }

            # Extract text blocks
            text_blocks = []
            for block in page.get_text("blocks"):
                if block[6] == 0:
                    text = block[4].strip().rstrip("-")  # Remove trailing hyphens
                    text = " ".join(text.split())  # Normalize spaces
                    if text and len(text) < 100:  
                        prev_text_blocks.append(text)  # Store short text
                    else:
                        if prev_text_blocks:
                            text = " ".join(prev_text_blocks) + " " + text
                            prev_text_blocks.clear()  # Merge and reset
                        
                        text_blocks.append({
                            "text": text,
                            "bbox": list(fitz.Rect(block[:4]))
                        })
            
            # Extract images
            images = []
            for img_index, img in enumerate(page.get_images(full=True)):
                if img in images:
                    continue
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_path = f"page{page.number+1}_img{img_index}.{base_image['ext']}"
                
                minio_client.upload_bytes(
                    image_path,
                    base_image["image"],
                    f"Images/{pdf_name}"
                )
                
                img_data = {
                    "path": image_path,
                    "bbox": list(page.get_image_bbox(img))
                }
                images.append(img_data)

            # If no text or all text is too short, store images in prev_images
            if not text_blocks or sum(len(tb["text"]) for tb in text_blocks) < 50:
                prev_images.extend(images)
            else:
                # Merge previous short texts and images into this page
                if prev_text_blocks:
                    text_blocks.insert(0, {
                        "text": " ".join(prev_text_blocks),
                        "bbox": []
                    })
                    prev_text_blocks.clear()

                if prev_images:
                    images.extend(prev_images)
                    prev_images.clear()

            # Add final text and images to page data
            page_data["text_blocks"] = text_blocks
            page_data["images"] = images
            pdf_data.append(page_data)
        
        results.append({"document": minio_file_name, "pages": pdf_data})
    
    return results



def format_extracted_data(raw_data: List[Dict],current_user:UserTenantDB) -> List[Dict]:
    formatted = []
    created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    for doc in raw_data:
        created_at = (datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S") + timedelta(seconds=1)).strftime("%Y-%m-%d %H:%M:%S")
        pdf_name = os.path.basename(doc["document"])
        for page in doc["pages"]:
            text=" ".join([b["text"] for b in page["text_blocks"]])
            if len(text)<50:
                continue
            formatted.append({
                "text": text,
                "metadata": {
                    "page_number": page["page_number"],
                    "images": [img["path"] for img in page["images"]],
                    "source": pdf_name,
                    "hash_id": hashlib.sha256(f"{pdf_name}_{page['page_number']}".encode()).hexdigest(),
                    "updated_by": current_user.user.id
                }
            })
    return formatted

def create_nodes(formatted_data):
    """Unecessary computation, mathi format_extracted_data mai last ma append garda Document banayera append gardeu
    Also you are looping through same list in multiple of these functions, yo sabai eutai loop ma garna milne khalko xa 
    """
    time = datetime.now()
    nodes=[]
    for i,doc in enumerate(formatted_data):
        created_at = (time + timedelta(seconds=i)).strftime("%Y-%m-%d %H:%M:%S")

        doc["metadata"]["created_at"] = created_at
        doc["metadata"]["updated_at"] = created_at
        nodes.append(Document(text=doc.get("text"), metadata=doc.get("metadata"))) 
    return nodes



async def handle_metadata(nodes: List[Document], minio,extra:Optional[list] = None):
    """Rename this to assign_urls_for_source_and_images and write a doc string"""
    results = []
  
    for node in nodes:
        # node=node.dict()
        node_m = node.metadata
        
        # remove every embedding field in the metadata as embeddings are too big for json output.
        new_node_m = {key: val for key, val in node_m.items() if "embedding" not in key}
        
        node_dict = node.dict()
        node_dict["metadata"] = new_node_m
        node_dict["text"] = node.text
        node = Document(**node_dict) # bad code that changes the value of list while looping through it.
        
        file = node.metadata.get("source")
        if file:
            presigned_url = await get_presigned_url_for_files([file], minio,"Files")
            if presigned_url:
                node.metadata["source_url"] = presigned_url[0]  # Assuming only one file

            # Handle images
        images = node.metadata.get("images", [])
        if images:
            presigned_urls = await get_presigned_url_for_files(images, minio,f"Images/{file}")
            node.metadata["images_url"] = presigned_urls

            # Add the node to the results list
        results.append(node)
    return results

async def get_presigned_url_for_files(files: List[str], minio: MinIOClient, folder: str) -> List[Dict[str, str]]:
    """
    Get presigned URLs for a list of files from MinIO storage.
    
    Args:
        files: List of file names/paths
        minio: MinIO client instance
        folder: Base folder path in MinIO
        
    Returns:
        List of dictionaries containing file names and their presigned URLs
    """
    async def get_url(file_str: str) -> Optional[Dict[str, str]]:
        try:
            if file_str.startswith(("http://", "https://")):
                return {"name": file_str, "url": file_str}
            presigned_url = minio.get_presigned_url(file_str, folder)
            return {"name": file_str, "url": presigned_url}
        except Exception as e:
            # loggers.error(f"\nError getting presigned URL for {file_str}: {e}")
            return None

    urls = await asyncio.gather(*[get_url(i) for i in files])
    return [url for url in urls if url is not None]



import pdfkit
import requests
import re
from bs4 import BeautifulSoup, Comment


async def process_urls(urls: Optional[str]) -> List[UploadFile]:
    if not urls:
        return []
    
    if not isinstance(urls, list):
        urls = urls.split(",")
        
    loggers.info(f"Processing URLs: {len(urls)}")
        
    # Create a list of tasks for parallel processing
    tasks = [process_single_url(url) for url in urls]
    
    # Run tasks in parallel using asyncio.gather
    pdfs = await asyncio.gather(*tasks)
    
    # Filter out any None values (failed tasks)
    pdfs = [pdf for pdf in pdfs if pdf is not None]
    
    return pdfs
async def generate_filename(url: str) -> str:
    """Generates a filename based on the last part of the URL."""
    if not url or not isinstance(url, str):
        raise ValueError("Invalid URL provided")

    # Extract the base name without extension
    pre_name = os.path.splitext(url.rstrip("/").split("/")[-1])[0]

    # Fallback: take the second-to-last segment if empty
    if not pre_name and "/" in url:
        parts = url.rstrip("/").split("/")
        pre_name = parts[-2] if len(parts) > 1 else ""

    # Final fallback: last 10 characters of the URL
    if not pre_name:
        pre_name = url[-10:].replace("/", "_")  # Replace slashes to avoid issues

    return f"{pre_name}_website.pdf"

async def process_single_url(url: str) -> Optional[UploadFile]:
    """
    Process a single URL and return an UploadFile object.
    Returns None if an error occurs.
    """
    try:
        pdf_byte = await asyncio.to_thread(clean_html, url)  # Run synchronous function in a thread
        pre_name= await generate_filename(url)
  
        filename = pre_name + "_website.pdf"
        pdf_file = UploadFile(file=BytesIO(pdf_byte), filename=filename)
        return pdf_file
    except Exception as e:
        print(f"Error processing URL {url}: {e}")
        return None

def clean_html(url):
    """
    Clean HTML content by removing non-essential elements while preserving main content,
    images, tables, and structural elements. Maintains charset for proper encoding.
    """
    try:
        html_content = requests.get(url).text
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove header-related elements
        header_selectors = [
            'header', 'nav', 
            '.header', '#header', '.navbar', '#navbar',
            '.top-bar', '#top-bar', '.main-nav', '.site-nav'
        ]
        for selector in header_selectors:
            for element in soup.select(selector):
                element.decompose()

        # Remove footer-related elements
        footer_selectors = [
            'footer', 
            '.footer', '#footer', '.site-footer',
            '.copyright', '#copyright', '.bottom-bar'
        ]
        for selector in footer_selectors:
            for element in soup.select(selector):
                element.decompose()

        # Remove script and style elements
        for element in soup.find_all(['script', 'style', 'link']):
            element.decompose()

        # Handle meta tags - preserve charset information
        for meta in soup.find_all('meta'):
            charset = meta.get('charset', '').lower()
            http_equiv = meta.get('http-equiv', '').lower()
            content = meta.get('content', '').lower()
            
            if charset in {'utf-8', 'utf8'}:
                continue
            elif http_equiv == 'content-type' and 'charset=' in content:
                continue
            else:
                meta.decompose()

        # Remove advertising and social media elements
        for selector in ['.ad', '#ad', '.social-widget', '.newsletter']:
            for element in soup.select(selector):
                element.decompose()

        # Clean inline attributes
        for tag in soup.find_all():
            # Remove JavaScript event handlers
            for attr in list(tag.attrs):
                if attr.startswith('on') or attr.startswith('data-'):
                    del tag[attr]
                    
            # Remove class and id attributes
            if 'class' in tag.attrs:
                del tag['class']
            if 'id' in tag.attrs:
                del tag['id']
                
            # Remove inline styles
            if 'style' in tag.attrs:
                del tag['style']

        # Remove HTML comments
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()

        # Clean empty elements (preserve semantic elements)
        PRESERVE_TAGS = {'img', 'br', 'hr', 'td', 'th', 'iframe', 'svg'}
        for tag in soup.find_all():
            if tag.name in PRESERVE_TAGS:
                continue
                
            if not tag.get_text(strip=True) and not tag.find_all(PRESERVE_TAGS):
                if tag.name not in PRESERVE_TAGS:
                    tag.decompose()

        # Clean whitespace and format
        cleaned_html = soup.prettify()
        cleaned_html = re.sub(r'\n{3,}', '\n\n', cleaned_html)  # Remove excessive newlines
        
        # Configure pdfkit with the path to wkhtmltopdf
        return makepdf(cleaned_html)
    
    except Exception as e:
        print(f"Error cleaning HTML from {url}: {e}")
        raise
from weasyprint import HTML


def makepdf(url):
    """Generate a PDF file from a string of HTML."""
    # htmldoc = HTML(string=html, base_url="")
    # return htmldoc.write_pdf()
    return HTML(url).write_pdf()