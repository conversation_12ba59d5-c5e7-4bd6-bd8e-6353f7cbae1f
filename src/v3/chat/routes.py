# Standard library imports
from datetime import datetime
from bson import ObjectId

# Third-party imports
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from nest_asyncio import apply

# Local imports - Core
from src.core.security import get_tenant_info

# Local imports - Models
from src.models.user import UserTenantDB
from src.v2.models.chat_hist import (
    ModelRunRequest,
    ChatHistMessage,
    ReplyResponse,
    CurrentUserContext,
)
from src.models.credit import CreditManager, get_credit_info

# Local imports - Helpers
from src.helper.logger import setup_new_logging


# Local imports - Services
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
from src.reply.minio_client import MinIOClient, MinIOConfig

# Local imports - Background Tasks
from src.background_tasks.reply_processing import (
    schedule_source_node_processing,
    process_source_nodes,
)

from src.v3.chat.dynamic_reply import generate_response_openai, MsgRequest

from src.v2.settings.ai_enable_disable.config import (
    get_ai_status_config,
    update_ai_status_config,
    is_ai_currently_enabled
)

# Initialize logging
loggers = setup_new_logging(__name__)

# Apply nest_asyncio to allow nested event loops
apply()

# Create an APIRouter instance
v3_reply_router = APIRouter()


@v3_reply_router.post("/chat/reply_generate")
async def generate_reply(
    request: ModelRunRequest,
    background_tasks: BackgroundTasks,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Generates a reply based on the chat history and user context.

    Args:
        request (ModelRunRequest): The request object containing chat history and user details.
        current_user (UserTenantDB): The current user's details.

    Returns:
        Dict[str, Any]: Response with the generated reply, chat summary, context, and spin states.

    Raises:
        HTTPException: If an error occurs during processing.
    """
    try:
        request_time = datetime.now()
        # Check if AI is enabled for the current channel
        ai_config = await get_ai_status_config(current_user.async_db)
        ai_enabled = is_ai_currently_enabled(ai_config, request.channel)
        has_credit = True

        # Get credit document and per_cost value
        credit_manager = CreditManager(current_user.db)
        # check channel
        if request.channel == "Whatsapp":
            per_cost, remaining_credit = get_credit_info( cost_type="whatsapp_cost+AI",current_user=current_user)
        else:
            per_cost, remaining_credit = get_credit_info( cost_type="AI_cost",current_user=current_user)


        CurrentUserContext.set_current_user(current_user)

        # qd_Config fetch
        env_var = current_user.db.settings.find_one({"name": "env"})
        qd_config_ = env_var.get("qdrant_config")

        qd_client = Qdrant_Call(config=QdrantConfig(**current_user.qdrant_config))
        minio = MinIOClient(config=MinIOConfig(**current_user.minio_config))

        loggers.log_dict_as_table(request.model_dump(), "Request data: ")

        # Extract chat history and latest message
        chat_hist, latest_message, image_process_metadata = await request.get_formatted_messages(
            current_user=current_user
        )
        # Set content in chat_data if it exists
        if hasattr(request, 'chat_data') and request.chat_data:
            request.chat_data[0]["data"]["content"] = latest_message.content
        latest_message.has_credit = has_credit

        # --------------- new type call --------------------------

        db_ = current_user.async_db

        # Prepare the base request data with profile and conversation_id
        request_data = request.model_dump()

        print(f"{request_data=}")

        response = {
            "request": request_data,
            "response": {
                "request_time": request_time,
                "processing_time": 0.0,
                "reply": " ",
                "information_gathering": [],
                "chat_ids": [str(latest_message.id)],
                "chat_data": [latest_message.model_dump_mongo()],
                "latest_message": latest_message.content,
                "has_credit": " ",
                "metadata": [],
                "language": " ",
                "reply_urls": [],
                "source_nodes": [],
                "call_to_action": [],
                # "identified_product": "",
                # "identified_product": [],
            },
            "created_at": datetime.now(),
        }

        # Add profile and conversation_id to the response if they exist in the request
        if request.profile:
            response["request"]["profile"] = request.profile.model_dump() if hasattr(request.profile, 'model_dump') else request.profile
        if request.conversation_id:
            response["request"]["conversation_id"] = request.conversation_id

        if not request.channel=="Playground":
            if remaining_credit < per_cost:
                response["response"]["has_credit"] = False
                latest_message.has_credit = False  

                pre_save = current_user.db.ai_response.insert_one(response)
                request_msg_id = pre_save.inserted_id


                chat_ids = []
                inserted_message = current_user.db.chat_messages.insert_one(
                    latest_message.model_dump_mongo()
                )
                chat_ids.append(str(inserted_message.inserted_id))


                raise HTTPException(status_code=402, detail=f"Insufficient credits. Required: {per_cost}, Available: {remaining_credit}")
        
        pre_save = current_user.db.ai_response.insert_one(response)
        request_msg_id = pre_save.inserted_id


        chat_ids = []
        inserted_message = current_user.db.chat_messages.insert_one(
            latest_message.model_dump_mongo()
        )
        chat_ids.append(str(inserted_message.inserted_id))

        # Initialize variables that will be used regardless of AI status
        source_nodes = []
        gathered_results = {}
        tool_calls_results = []
        metadata_list = []
        image_names = set()


        if ai_enabled:
            # --- Resolve System prompt: Elaborated or Simplified from Database ----

            AI_REPLY_MODE = request.mode

            # AI_REPLY_MODE = await db_.settings.find_one({"name": "AI_REPLY_MODE"})
            # try: AI_REPLY_MODE = AI_REPLY_MODE.get("value", "None")
            # except: AI_REPLY_MODE = "None"

            if AI_REPLY_MODE == "simplified":
                PROMPT_ = await db_.prompt.find_one(
                    {"name": "reply_prompt_openai_simplified"}
                )
            elif AI_REPLY_MODE == "elaborated":
                PROMPT_ = await db_.prompt.find_one(
                    {"name": "reply_prompt_openai_elaborated"}
                )
            else:
                PROMPT_ = await db_.prompt.find_one({"name": "reply_prompt_openai"})

            SYSTEM_PROMPT = PROMPT_
            # --- Resolve System prompt: Resolved----------- ------------------------

            reply, gathered_results = generate_response_openai(
                MsgRequest(
                    customer_id=request.user_id,
                    message=latest_message.content,
                    message_media_values=latest_message.media_values,
                    image_process_metadata=image_process_metadata,
                    previous_summary=[chat.model_dump_promp() for chat in chat_hist],
                    channel=request.channel,
                ),
                SYSTEM_PROMPT,
                qd_client,
                current_user,
            )
 
            # credit_result = credit_manager.deduct_credits(
            #     amount=total_file_cost,
            #     description="Processing documents"
            # )
            
            # if not credit_result["success"]:
            #     loggers.error(f"Failed to deduct credits: {credit_result['message']}")

            reply_str = reply
            response["response"]["reply"] = reply
            gathered_results.update({"Response": reply})
            loggers.log_dict_as_table(
                gathered_results, f"Results for UserID: {request.user_id}: "
            )

            # Limit the number of source nodes to process (max 10)
            source_nodes = gathered_results.get("source_nodes", [])[:10]

            # For WhatsApp, extract URLs directly from source nodes
            # if request.channel and request.channel.lower() == "whatsapp" and source_nodes:
            if source_nodes:
                # Extract URLs directly from source nodes
                extracted_urls = []
                for node in source_nodes:
                    # Check if the node has resource_url in metadata
                    resource_url = node.metadata.get("resource_url")
                    if resource_url:
                        loggers.info(f"Found resource_url in node: {resource_url}")
                        if isinstance(resource_url, list):
                            for url in resource_url:
                                if isinstance(url, dict) and "url" in url:
                                    extracted_urls.append(url["url"])
                                elif isinstance(url, str):
                                    extracted_urls.append(url)
                        elif isinstance(resource_url, str):
                            extracted_urls.append(resource_url)

                # If we found URLs, add them to the response
                if extracted_urls:
                    # Remove duplicates
                    extracted_urls = list(set(extracted_urls))
                    # loggers.info(
                    #     f"Extracted {len(extracted_urls)} URLs directly from source nodes: {extracted_urls}"
                    # )
                    response["response"]["reply_urls"] = extracted_urls

            # Initialize empty collections for immediate response
            metadata_list = []
            image_names = set()

            # Schedule background processing of source nodes
            # This will update the database with metadata after the response is sent
            # For WhatsApp, we need to wait for processing to complete to get media URLs
            loggers.info(f"Channel detected: {request.channel}")
            # if request.channel and request.channel.lower() == "whatsapp":
            if request.channel:

                # We've already extracted URLs directly from source nodes above
                # Also check for images in the source nodes
                try:
                    # Extract image URLs directly
                    for node in source_nodes:
                        source = node.metadata.get("source")
                        if source:
                            images = node.metadata.get("images", [])
                            for image in images[:5]:  # Limit to 5 images
                                try:
                                    image_url = minio.get_presigned_url(
                                        image, f"Images/{source}"
                                    )
                                    loggers.info(
                                        f"Generated image URL for WhatsApp: {image_url}"
                                    )
                                    # Add to reply_urls if not already there
                                    if image_url not in response["response"]["reply_urls"]:
                                        response["response"]["reply_urls"].append(image_url)
                                except Exception as e:
                                    loggers.error(
                                        f"Error getting image URL for {image}: {e}"
                                    )

                    # Log the final URLs
                    loggers.info(f"response: {response['response']['reply']}")

                    # Make sure the URLs are also in the database along with profile and conversation_id
                    update_fields = {
                        "response.reply_urls": response["response"]["reply_urls"]
                    }

                    # Include profile and conversation_id in the update if they exist
                    if request.profile:
                        update_fields["request.profile"] = request.profile.model_dump() if hasattr(request.profile, 'model_dump') else request.profile
                    if request.conversation_id:
                        update_fields["request.conversation_id"] = request.conversation_id

                    current_user.db.ai_response.update_one(
                        {"_id": ObjectId(request_msg_id)},
                        {"$set": update_fields},
                    )
                except Exception as e:
                    loggers.error(f"Error processing images for WhatsApp: {e}")
            # else:
            #     # For other channels, use background processing as before
            #     schedule_source_node_processing(
            #         background_tasks,
            #         str(request_msg_id),
            #         source_nodes,
            #         qd_client,
            #         qd_config_,
            #         minio,
            #         current_user,
            #     )

            reply = ChatHistMessage.format_message(
                role="assistant",
                content=reply_str,
                media_ids=response["response"]["reply_urls"],
                media_values="",
                user_id=latest_message.user_id,
                ai_enabled=ai_enabled
            )
            try:
                reply.media_ids = list(image_names)
                inserted_reply = current_user.db.chat_messages.insert_one(
                    reply.model_dump_mongo()
                )

            except Exception as e:
                loggers.error(f"Error inserting reply: {e}")
                reply = reply.model_dump_mongo()
                reply["media_ids"] = list(image_names)
                inserted_reply = current_user.db.chat_messages.insert_one(reply)
            reply.media_ids = list(image_names)
            chat_ids.append(str(inserted_reply.inserted_id))
            chat_hist.append(reply)

            latest_chat_data = latest_message.model_dict_format()
            reply_data = reply.model_dict_format()
            reply_data["media_ids"] = list(image_names)

            chat_data = [latest_chat_data, reply_data]

            tool_calls_results = gathered_results.get("tool_calls_results", [])

            # Construct final response
            loggers.info(f"Final response: {reply}")
            cta = []

            for tool_call in tool_calls_results:
                if tool_call.get("function_name") == "create_issue_tickets":
                    # Check if both cta_id and issue_type are present and not null/empty
                    function_args = tool_call.get("function_args", {})
                    cta_id = function_args.get("cta_id")
                    issue_type = function_args.get("issue_type")

                    # Skip if either cta_id OR issue_type is missing/null/empty
                    if not cta_id or not issue_type:
                        print(f"Skipping CTA creation - cta_id: '{cta_id}', issue_type: '{issue_type}'")
                        continue

                    cta.append(
                        {
                            "id": cta_id,
                            "type": issue_type,
                            "status": "open",
                        }
                    )
                elif tool_call.get("function_name") == "handle_booking":
                    # print(tool_call)
                    # Check if both cta_id and issue_type are present and not null/empty
                    function_args = tool_call.get("function_args", {})
                    cta_id = function_args.get("cta_id")
                    issue_type = function_args.get("issue_type")

                    # Skip if either cta_id OR issue_type is missing/null/empty
                    if not cta_id or not issue_type:
                        print(f"Skipping booking CTA creation - cta_id: '{cta_id}', issue_type: '{issue_type}'")
                        continue

                    cta.append(
                        {
                            "id": cta_id,
                            "type": issue_type,
                            "status": "open",
                        }
                    )
        else:
            # AI is disabled - just record the user message without AI processing
            loggers.info("AI is disabled. Recording user message without AI processing.")

            # No assistant reply is created when AI is disabled
            # Just use the latest user message for the response
            reply_str= ""

            # Format chat data - only include the user message
            latest_chat_data = latest_message.model_dict_format()
            chat_data = [latest_chat_data]

            # Empty call to action when AI is disabled
            cta = []

        # Prepare response structure based on AI status
        final_request_data = request.model_dump()

        response = {
            "request": final_request_data,
            "response": {
                "request_time": request_time,
                "processing_time": (datetime.now() - request_time).total_seconds(),
                "reply": reply_str,
                "information_gathering": tool_calls_results,
                "chat_ids": chat_ids,
                "chat_data": chat_data,
                "latest_message": latest_message.content,
                "metadata": metadata_list,
                # "identified_product": gathered_results.get("identified_product", None),
                # "identified_product": gathered_results.get("identified_product", None),
                "language": gathered_results.get("language", None),
                "background_processing": ai_enabled,  # Only true if AI is enabled
                "background_processing_completed": not ai_enabled,  # Completed immediately if AI disabled
                "reply_urls": response["response"].get("reply_urls", []),
                "source_nodes": [node.model_dump(mode="json") for node in source_nodes] if source_nodes else [],
                "call_to_action": cta,
                "ai_enabled": ai_enabled , # Add flag to indicate if AI was enabled for this request
                "has_credit":True
            },
            "created_at": datetime.now(),
        }

        # Ensure profile and conversation_id are included in the final response
        if request.profile:
            response["request"]["profile"] = request.profile.model_dump() if hasattr(request.profile, 'model_dump') else request.profile
        if request.conversation_id:
            response["request"]["conversation_id"] = request.conversation_id

        # ai response=
        # ai_response_id = current_user.db.ai_response.insert_one(response)
        # Update the existing document with the response data
        update_data = {
            "request_time": request_time,
            "processing_time": (datetime.now() - request_time).total_seconds(),
            "reply": reply_str if ai_enabled else "",  # Empty reply when AI is disabled
            # "identified_product": gathered_results.get("identified_product", None),
            "information_gathering": tool_calls_results,
            "chat_ids": chat_ids,
            "chat_data": chat_data,
            "latest_message": latest_message.content,
            "metadata": metadata_list,
            "language": gathered_results.get("language", None),
            "background_processing": ai_enabled,  # Only true if AI is enabled
            "background_processing_completed": not ai_enabled,  # Completed immediately if AI disabled
            "reply_urls": response["response"].get("reply_urls", []),
            "source_nodes": [node.model_dump(mode="json") for node in source_nodes] if source_nodes else [],
            "call_to_action": cta,
            "ai_enabled": ai_enabled,  # Add flag to indicate if AI was enabled for this request
            "has_credit":True
        }

        # Add usage data only if AI was enabled
        if ai_enabled:
            update_data["usage"] = {
                "prompt_tokens": gathered_results.get("token_usage", {}),
                "image_process_cost": gathered_results.get("image_process_cost", {})
            }

            loggers.info(f"Deducting credits for request {request_msg_id} channel {request.channel}")

            if not request.channel == "Playground":
                credit_result = credit_manager.deduct_credits(
                    # amount=per_cost if request.channel != "Whatsapp" else 3,
                    amount=per_cost if per_cost else 3,
                    description="AI Response" if request.channel != "Whatsapp" else "AI Response (Whatsapp)",
                    message_id=str(request_msg_id)
                )
                if not credit_result["success"]:
                    loggers.error(f"Failed to deduct credits while generating reply: {credit_result['message']}")
        complete_update_data = {
            "response": update_data,
            "request": request.model_dump()
        }

        # Ensure profile and conversation_id are included in the request data
        if request.profile:
            complete_update_data["request"]["profile"] = request.profile.model_dump() if hasattr(request.profile, 'model_dump') else request.profile
        if request.conversation_id:
            complete_update_data["request"]["conversation_id"] = request.conversation_id


        # Update the database
        current_user.db.ai_response.update_one(
            {"_id": ObjectId(request_msg_id)},
            {"$set": complete_update_data},
        )
   
        # add message_id in message
        current_user.db.chat_messages.update_one(
            {"_id": inserted_message.inserted_id},
            {"$set": {"message_id": str(request_msg_id)}},
        )

        # Only update the reply message if AI was enabled (and reply was created)
        if ai_enabled:
            current_user.db.chat_messages.update_one(
                {"_id": inserted_reply.inserted_id},
                {"$set": {"message_id": str(request_msg_id)}},
            )

        # Only process CTA updates if AI was enabled
        if ai_enabled and tool_calls_results:
            # loop through tool_call_results, see if any is a create_issue_tickets, if it is, update the cta to have the response_id
            # also format the tool_call_results to only show things frontend needs to see.
            for tool_call in tool_calls_results:
                if tool_call.get("function_name") == "create_issue_tickets":
                    # Only update if cta_id exists and is not null/empty
                    cta_id = tool_call.get("function_args", {}).get("cta_id")
                    if cta_id:
                        try:
                            current_user.db.cta.update_one(
                                {"_id": ObjectId(cta_id)},
                                {"$set": {"response_id": str(request_msg_id)}},
                            )
                        except Exception as e:
                            print(f"Error updating CTA {cta_id}: {e}")
                    else:
                        print("Skipping CTA update - no valid cta_id found")

                elif tool_call.get("function_name") == "handle_booking":
                    # Only update if cta_id exists and is not null/empty
                    cta_id = tool_call.get("function_args", {}).get("cta_id")
                    if cta_id:
                        try:
                            current_user.db.cta.update_one(
                                {"_id": ObjectId(cta_id)},
                                {"$set": {"response_id": str(request_msg_id)}},
                            )
                        except Exception as e:
                            print(f"Error updating booking CTA {cta_id}: {e}")
                    else:
                        print("Skipping booking CTA update - no valid cta_id found")

        return response,str(request_msg_id)
    except HTTPException as e:
        raise e
    except Exception as e:
        loggers.exception("Error in generate_reply")
        raise HTTPException(status_code=500, detail=str(e))
