from src.v3.chat.routes import v3_reply_router
# from src.v3.chat.agent_sdk_tool.api import agent_router
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

v3_app = FastAPI(
    title="Echo Bot API v3",
    description="Version 3 of the Echo Bot API",
    version="3.0.0"
)

def custom_openapi():
    if v3_app.openapi_schema:
        return v3_app.openapi_schema

    openapi_schema = get_openapi(
        title=v3_app.title,
        version=v3_app.version,
        routes=v3_app.routes,
        servers=[{"url": "http://localhost:8000/v3", "description": "V3 Chat API Server"}]
    )

    # Force the correct token URL
    openapi_schema["components"]["securitySchemes"] = {
        "OAuth2PasswordBearer": {
            "type": "oauth2",
            "flows": {
                "password": {
                    "tokenUrl": "http://localhost:8000/login",  # Absolute URL
                    "scopes": {}
                }
            }
        }
    }

    v3_app.openapi_schema = openapi_schema
    return v3_app.openapi_schema

v3_app.openapi = custom_openapi
v3_app.include_router(v3_reply_router, tags=["Chat v3"])

# redirect / to docs



# v3_app.include_router(agent_router, tags=["Agents"])